// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ================================
// CORE USER MANAGEMENT
// ================================

model User {
  id        Int       @id @default(autoincrement())
  email     String?   @unique
  username  String    @unique
  password  String?
  firstName String?
  lastName  String?
  avatar    String?
  bio       String?
  phone     String?   @unique
  ageRange  AgeRange?
  gender    Gender?
  country   String?
  role      Role?   @default(USER)

  // User status and verification
  isActive            Boolean   @default(true)
  isVerified          Boolean   @default(false)
  lastLoginAt         DateTime?
  interests           String?
  areasForImprovement String?

  // Gamification
  totalPoints     Int @default(0)
  availablePoints Int @default(0) // Points not yet withdrawn
  level           Int @default(1)

  //otp
  otp        String?
  otpExpires DateTime?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  companies      Company[]
  createdDares   Dare[]          @relation("DareCreator")
  attemptedDares AttemptedDare[]
  withdrawals    Withdrawal[]
  reactions      Reaction[]
  comments       Comment[]
  ads            Ad[]
  notifications  Notification[]
  requests       Request[]

  @@map("users")
}

// ================================
// COMPANY MANAGEMENT
// ================================

model Company {
  id          Int     @id @default(autoincrement())
  name        String
  description String?
  logo        String?
  website     String?
  email       String?
  phone       String?
  country     String?

  // Company type and verification
  type       CompanyType
  isVerified Boolean     @default(false)
  isActive   Boolean     @default(true)

  // Creator/Owner
  creatorId Int
  creator   User @relation(fields: [creatorId], references: [id])

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dares Dare[] @relation("CompanyDares")
  ads   Ad[]

  @@map("companies")
}

// ================================
// DARE SYSTEM
// ================================

model DareCategory {
  id          Int     @id @default(autoincrement())
  name        String  @unique
  description String?
  icon        String?
  color       String? // Hex color for UI
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  dares Dare[]

  @@map("dare_categories")
}

model Dare {
  id           Int     @id @default(autoincrement())
  title        String
  description  String
  instructions String? // Step-by-step instructions

  // Difficulty and rewards
  difficulty   DifficultyLevel
  pointsToEarn Int
  pointsToLose Int
  timeLimit    Int? // Time limit in seconds

  // Category and creator
  categoryId Int
  category   DareCategory @relation(fields: [categoryId], references: [id])

  // Creator (can be user or company)
  creatorId Int?
  creator   User?    @relation("DareCreator", fields: [creatorId], references: [id])
  companyId Int?
  company   Company? @relation("CompanyDares", fields: [companyId], references: [id])

  // Status and moderation
  isActive   Boolean @default(true)
  isFeatured Boolean @default(false)

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  attempts AttemptedDare[]

  @@map("dares")
}

model AttemptedDare {
  id     Int @id @default(autoincrement())
  userId Int
  dareId Int

  // Attempt details
  status      AttemptStatus @default(IN_PROGRESS)
  videoUrl    String? // Video url
  description String? // User's description of their attempt

  // Scoring
  pointsEarned Int     @default(0)
  pointsLost   Int     @default(0)
  isSuccessful Boolean @default(false)

  // Timing
  startedAt   DateTime  @default(now())
  completedAt DateTime?

  // Moderation
  isVerified Boolean   @default(false)
  verifiedAt DateTime?
  verifiedBy Int? // Admin who verified

  // Relations
  user      User       @relation(fields: [userId], references: [id])
  dare      Dare       @relation(fields: [dareId], references: [id])
  reactions Reaction[]
  comments  Comment[]

  @@unique([userId, dareId]) // User can only attempt each dare once
  @@map("attempted_dares")
}

// ================================
// GAMIFICATION & REWARDS
// ================================

model AreaForImprovement {
  id          Int     @id @default(autoincrement())
  name        String  @unique
  description String?
  icon        String?
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("area_for_investments")
}

model Interest {
  id          Int     @id @default(autoincrement())
  name        String  @unique
  description String?
  icon        String?

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("interests")
}

model PointsValue {
  id          Int     @id @default(autoincrement())
  name        String // e.g., "Cash Reward", "T-Shirt", "Gift Card"
  description String?
  country     String?

  // Reward details
  pointsCost Int //eg 100 points = 1 t-shirt
  quantity   Int // e.g., 1

  // Availability
  isActive Boolean @default(true)

  // Media
  icon String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  withdrawals Withdrawal[]

  @@map("points_value")
}

model Withdrawal {
  id            Int @id @default(autoincrement())
  userId        Int
  pointsValueId Int

  // Withdrawal details
  pointsSpent Int
  status      WithdrawalStatus @default(PENDING)

  // Delivery information (for physical rewards)
  deliveryAddress Json?
  trackingNumber  String?

  // Processing
  processedAt DateTime?
  deliveredAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id])
  pointsValue PointsValue @relation(fields: [pointsValueId], references: [id])

  @@map("withdrawals")
}

// ================================
// SOCIAL FEATURES
// ================================

model Reaction {
  id     Int @id @default(autoincrement())
  userId Int

  // What is being reacted to
  attemptedDareId Int?

  // Reaction type
  type ReactionType // LIKE, INSPIRED, NAILED_IT, etc.

  createdAt DateTime @default(now())

  // Relations
  user          User           @relation(fields: [userId], references: [id])
  attemptedDare AttemptedDare? @relation(fields: [attemptedDareId], references: [id])

  // Ensure user can only react once per item
  @@unique([userId, attemptedDareId])
  @@map("reactions")
}

model Comment {
  id              Int    @id @default(autoincrement())
  userId          Int
  content         String
  attemptedDareId Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user          User           @relation(fields: [userId], references: [id])
  attemptedDare AttemptedDare? @relation(fields: [attemptedDareId], references: [id])

  @@map("comments")
}

// ================================
// ADVERTISING SYSTEM
// ================================

model AdSpot {
  id          Int     @id @default(autoincrement())
  name        String  @unique // e.g., "Home Page Banner", "Dare Page Sidebar"
  description String?

  // Spot configuration
  width       Int?
  height      Int?
  maxFileSize Int? // in bytes

  // Pricing
  costPerView  Decimal? @db.Decimal(10, 2)
  costPerClick Decimal? @db.Decimal(10, 2)
  costPerDay   Decimal? @db.Decimal(10, 2)

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ads Ad[]

  @@map("ads_spots")
}

model Ad {
  id       Int     @id @default(autoincrement())
  title    String
  content  String?
  imageUrl String?
  videoUrl String?

  // Targeting
  targetUrl String // Where the ad links to

  // Creator (user or company)
  userId    Int?
  companyId Int?

  // Ad placement
  adSpotId Int

  // Campaign details
  budget      Decimal @db.Decimal(10, 2)
  spentAmount Decimal @default(0) @db.Decimal(10, 2)

  // Scheduling
  startDate DateTime
  endDate   DateTime?

  // Performance tracking
  impressions Int @default(0)
  clicks      Int @default(0)

  // Status
  status   AdStatus @default(PENDING)
  isActive Boolean  @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User?    @relation(fields: [userId], references: [id])
  company Company? @relation(fields: [companyId], references: [id])
  adSpot  AdSpot   @relation(fields: [adSpotId], references: [id])

  @@map("ads")
}

// ================================
// COMMUNICATION SYSTEM
// ================================

model Notification {
  id     Int @id @default(autoincrement())
  userId Int

  // Notification content
  title   String
  message String
  type    NotificationType

  // Related entities
  entityType String? // "dare", "attempt", "comment", etc.
  entityId   Int? // ID of the related entity

  // Status
  isRead Boolean   @default(false)
  readAt DateTime?

  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("notifications")
}

model Broadcast {
  id      Int    @id @default(autoincrement())
  title   String
  message String

  // Targeting
  targetAudience BroadcastAudience @default(ALL_USERS)

  // Scheduling
  scheduledAt DateTime?
  sentAt      DateTime?

  // Status
  status   BroadcastStatus @default(DRAFT)
  isActive Boolean         @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("broadcasts")
}

model Request {
  id     Int @id @default(autoincrement())
  userId Int

  // Request details
  type        RequestType
  title       String
  description String

  // Request data (flexible JSON for different request types)
  requestData Json?

  // Status and processing
  status      RequestStatus @default(PENDING)
  response    String? // Admin response
  processedAt DateTime?
  processedBy Int? // Admin who processed

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("requests")
}

// ================================
// ENUMS
// ================================

enum CompanyType {
  CREATOR // YouTuber, Influencer
  ARTIST // Musicians, Visual Artists
  ORGANIZATION // NGOs, Non-profits
  RESTAURANT // Food & Beverage
  SCHOOL // Educational Institutions
  UNIVERSITY // Higher Education
  BUSINESS // General Business
  BAR
  CLUB
  HOSPITAL
  PARK
}

enum DifficultyLevel {
  EASY
  MEDIUM
  HARD
  EXTREME
}

enum AttemptStatus {
  IN_PROGRESS
  SUBMITTED // Waiting for verification
  COMPLETED // Successfully completed
  FAILED // Did not complete successfully
  CANCELLED // User cancelled attempt
}

enum ReactionType {
  INSPIRED
  NAILED_IT
  CAN_DO_BETTER
  FUNNY
  MOTIVATING
}

enum RewardType {
  CASH
  GIFT_CARD
  AIRTIME
  MOBILE_DATA
  ADVERTISE
  MYSTERY_BOX
  VENCHAZ_MERCH
  FOOD
}

enum WithdrawalStatus {
  PENDING
  PROCESSING
  SHIPPED // For physical items
  DELIVERED // For physical items
  COMPLETED // For digital/cash rewards
  CANCELLED
  FAILED
}

enum AdStatus {
  PENDING // Awaiting approval
  APPROVED // Ready to run
  RUNNING // Currently active
  PAUSED // Temporarily stopped
  COMPLETED // Campaign finished
  REJECTED // Not approved
  CANCELLED // Cancelled by user
}

enum NotificationType {
  DARE_ATTEMPT // Someone attempted your dare
  DARE_COMMENT // Comment on your dare
  ATTEMPT_COMMENT // Comment on your attempt
  REACTION // Someone reacted to your content
  DARE_APPROVED // Your dare was approved
  DARE_REJECTED // Your dare was rejected
  POINTS_EARNED // You earned points
  WITHDRAWAL_UPDATE // Withdrawal status update
  SYSTEM // System notifications
  PROMOTIONAL // Marketing messages
}

enum BroadcastAudience {
  ALL_USERS
  ACTIVE_USERS // Users active in last 30 days
  NEW_USERS // Users registered in last 7 days
  HIGH_POINT_USERS // Users with points above threshold
  COMPANY_OWNERS // Users who own companies
}

enum BroadcastStatus {
  DRAFT
  SCHEDULED
  SENT
  CANCELLED
}

enum RequestType {
  REPORT_CONTENT // Report inappropriate content
  FEATURE_REQUEST // Suggest new features
  SUPPORT // General support request
  OTHER
}

enum RequestStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  COMPLETED
  CANCELLED
}

enum Gender {
  MALE
  FEMALE
  OTHER
  UNSPECIFIED
}

enum AgeRange {
  FROM_18_24
  FROM_25_34
  FROM_35_44
  FROM_45_54
  FROM_55_64
  OVER_65
  UNSPECIFIED
}

enum Role {
  USER
  ADMIN
  COMPANY_OWNER
}
