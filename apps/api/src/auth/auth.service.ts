import { Injectable, ConflictException, UnauthorizedException, BadRequestException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { DatabaseService } from '../database/database.service';
import { RegisterDto, LoginDto, VerifyOtpDto, ForgotPasswordDto, ResetPasswordDto, SetPasswordDto } from './dto';
import { User } from '../../generated/prisma';

@Injectable()
export class AuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto) {
    const { email, phone, country } = loginDto;

    if (!email && !phone) {
      throw new BadRequestException('Email or phone number is required');
    }

    // Check if user exists
    const existingUser = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          phone ? { phone } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    const isNewUser = !existingUser;
    
    // Generate OTP
    const otp = this.generateOtp();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    if (isNewUser) {
      // Create new user with minimal data
      await this.databaseService.user.create({
        data: {
          email: email || null,
          phone: phone || null,
          username: email ? email.split('@')[0] : `user_${Date.now()}`, // Temporary username
          otp,
          country,
          otpExpires,
          isVerified: false,
        }
      });
    } else {
      // Update existing user with new OTP
      await this.databaseService.user.update({
        where: { id: existingUser.id },
        data: {
          otp,
          otpExpires,
        }
      });
    }

    // TODO: Send OTP via email or SMS
    if (email) {
      // await this.sendOtpEmail(email, otp);
    } else if (phone) {
      // await this.sendOtpSms(phone, otp);
    }

    return {
      message: `OTP sent to ${email || phone}`,
      isNewUser,
      // For development, return OTP (remove in production)
      otp: process.env.NODE_ENV === 'development' ? otp : undefined,
    };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const { email, phone, otp } = verifyOtpDto;

    if (!email && !phone) {
      throw new BadRequestException('Email or phone number is required');
    }

    const user = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          phone ? { phone } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.otp || !user.otpExpires) {
      throw new BadRequestException('No OTP found for this user');
    }

    if (user.otpExpires < new Date()) {
      throw new BadRequestException('OTP has expired');
    }

    if (user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // Update user as verified and clear OTP
    const updatedUser = await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        isVerified: true,
        otp: null,
        otpExpires: null,
        lastLoginAt: new Date(),
      },
      select: {
        id: true,
        email: true,
        phone: true,
        username: true,
        firstName: true,
        country: true,
        lastName: true,
        role: true,
        isVerified: true,
      }
    });

    // Generate JWT token
    const payload = { 
      sub: user.id, 
      email: user.email, 
      phone: user.phone,
      username: user.username,
      role: user.role 
    };
    const accessToken = this.jwtService.sign(payload);

    return {
      message: 'Login successful',
      accessToken,
      user: updatedUser,
    };
  }

  async register(registerDto: RegisterDto, userId: number) {
    const { username, ...userData } = registerDto;

    // Check if user exists and is authenticated
    const user = await this.databaseService.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.isVerified) {
      throw new UnauthorizedException('Please verify your account first');
    }

    // Check if username is already taken
    const existingUsername = await this.databaseService.user.findFirst({
      where: {
        username,
        id: { not: userId }
      }
    });

    if (existingUsername) {
      throw new ConflictException('Username already exists');
    }

    // Update user with registration data
    const updatedUser = await this.databaseService.user.update({
      where: { id: userId },
      data: {
        username,
        ...userData,
      },
      select: {
        id: true,
        email: true,
        phone: true,
        username: true,
        firstName: true,
        lastName: true,
        gender: true,
        ageRange: true,
        country: true,
        bio: true,
        interests: true,
        areasForImprovement: true,
        role: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return {
      message: 'Registration completed successfully',
      user: updatedUser,
    };
  }

  async setPassword(setPasswordDto: SetPasswordDto, userId: number) {
    const { password } = setPasswordDto;

    const user = await this.databaseService.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.isVerified) {
      throw new UnauthorizedException('Please verify your account first');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    await this.databaseService.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
      }
    });

    return {
      message: 'Password set successfully',
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email, phone } = forgotPasswordDto;

    if (!email && !phone) {
      throw new BadRequestException('Email or phone number is required');
    }

    const user = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          phone ? { phone } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    if (!user) {
      // Don't reveal if user exists or not
      return {
        message: 'If the account exists, a password reset OTP has been sent.',
      };
    }

    // Generate OTP
    const otp = this.generateOtp();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        otp,
        otpExpires,
      }
    });

    // TODO: Send OTP via email or SMS
    if (email) {
      // await this.sendOtpEmail(email, otp);
    } else if (phone) {
      // await this.sendOtpSms(phone, otp);
    }

    return {
      message: 'If the account exists, a password reset OTP has been sent.',
      // For development, return OTP (remove in production)
      otp: process.env.NODE_ENV === 'development' ? otp : undefined,
    };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { email, phone, otp, newPassword } = resetPasswordDto;

    if (!email && !phone) {
      throw new BadRequestException('Email or phone number is required');
    }

    const user = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          phone ? { phone } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.otp || !user.otpExpires) {
      throw new BadRequestException('No OTP found for this user');
    }

    if (user.otpExpires < new Date()) {
      throw new BadRequestException('OTP has expired');
    }

    if (user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password and clear OTP
    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        otp: null,
        otpExpires: null,
      }
    });

    return {
      message: 'Password reset successfully',
    };
  }

  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // TODO: Implement email service
  // private async sendOtpEmail(email: string, otp: string) {
  //   // Implementation for sending OTP email
  // }

  // TODO: Implement SMS service
  // private async sendOtpSms(phone: string, otp: string) {
  //   // Implementation for sending OTP SMS
  // }
}
