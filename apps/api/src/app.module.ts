import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ServeStaticModule } from '@nestjs/serve-static';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { DareCategoriesModule } from './dare-categories/dare-categories.module';
import { InterestsModule } from './interests/interests.module';
import { AreasForImprovementModule } from './areas-for-improvement/areas-for-improvement.module';
import { DaresModule } from './dares/dares.module';
import { join } from 'path';

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '../..', 'dashboard', 'dist'),
    }),
    DatabaseModule,
    AuthModule,
    UserModule,
    DareCategoriesModule,
    InterestsModule,
    AreasForImprovementModule,
    DaresModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
