import { IsString, IsOptional, IsInt, Min, <PERSON>Length } from 'class-validator';

export class CreatePointsValueDto {
  @IsString()
  @MaxLength(100)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  country?: string;

  @IsInt()
  @Min(1)
  pointsCost: number;

  @IsInt()
  @Min(1)
  quantity: number;

  @IsOptional()
  @IsString()
  icon?: string;
}
