import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreatePointsValueDto, UpdatePointsValueDto } from './dto';

@Injectable()
export class PointsValueService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createPointsValueDto: CreatePointsValueDto) {
    try {
      const pointsValue = await this.databaseService.pointsValue.create({
        data: createPointsValueDto,
      });

      return {
        message: 'Points value created successfully',
        pointsValue,
      };
    } catch (error) {
      if (error.code === 'P2002') {
        throw new ConflictException('Points value with this name already exists');
      }
      throw error;
    }
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    isActive?: boolean,
    country?: string,
  ) {
    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (country) {
      where.OR = [
        { country: country },
        { country: null }, // Include global rewards
      ];
    }

    const [pointsValues, total] = await Promise.all([
      this.databaseService.pointsValue.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.databaseService.pointsValue.count({ where }),
    ]);

    return {
      pointsValues,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const pointsValue = await this.databaseService.pointsValue.findUnique({
      where: { id },
    });

    if (!pointsValue) {
      throw new NotFoundException('Points value not found');
    }

    return pointsValue;
  }

  async update(id: number, updatePointsValueDto: UpdatePointsValueDto) {
    try {
      const pointsValue = await this.databaseService.pointsValue.update({
        where: { id },
        data: updatePointsValueDto,
      });

      return {
        message: 'Points value updated successfully',
        pointsValue,
      };
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Points value not found');
      }
      if (error.code === 'P2002') {
        throw new ConflictException('Points value with this name already exists');
      }
      throw error;
    }
  }

  async remove(id: number) {
    try {
      await this.databaseService.pointsValue.delete({
        where: { id },
      });

      return {
        message: 'Points value deleted successfully',
      };
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Points value not found');
      }
      throw error;
    }
  }

  async toggleStatus(id: number) {
    const pointsValue = await this.findOne(id);

    const updatedPointsValue = await this.databaseService.pointsValue.update({
      where: { id },
      data: { isActive: !pointsValue.isActive },
    });

    return {
      message: `Points value ${updatedPointsValue.isActive ? 'activated' : 'deactivated'} successfully`,
      pointsValue: updatedPointsValue,
    };
  }
}
