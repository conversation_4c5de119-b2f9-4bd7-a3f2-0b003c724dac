import { IsString, Is<PERSON><PERSON>al, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateDareCategoryDto {
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  icon?: string;

  @IsOptional()
  @IsString()
  @MaxLength(7) // Hex color format #FFFFFF
  color?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
