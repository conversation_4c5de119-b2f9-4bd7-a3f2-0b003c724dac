import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateDareCategoryDto, UpdateDareCategoryDto } from './dto';
import { Prisma } from '../../generated/prisma';

@Injectable()
export class DareCategoriesService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createDareCategoryDto: CreateDareCategoryDto) {
    try {
      const category = await this.databaseService.dareCategory.create({
        data: {
          name: createDareCategoryDto.name,
          description: createDareCategoryDto.description,
          icon: createDareCategoryDto.icon,
          color: createDareCategoryDto.color,
          isActive: createDareCategoryDto.isActive ?? true,
        },
      });

      return {
        message: 'Dare category created successfully',
        category,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('A category with this name already exists');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, search?: string, isActive?: boolean) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.DareCategoryWhereInput = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, } },
        { description: { contains: search,  } },
      ];
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [categories, total] = await Promise.all([
      this.databaseService.dareCategory.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: { dares: true },
          },
        },
      }),
      this.databaseService.dareCategory.count({ where }),
    ]);

    return {
      categories,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const category = await this.databaseService.dareCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { dares: true },
        },
      },
    });

    if (!category) {
      throw new NotFoundException('Dare category not found');
    }

    return category;
  }

  async update(id: number, updateDareCategoryDto: UpdateDareCategoryDto) {
    try {
      const category = await this.databaseService.dareCategory.findUnique({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException('Dare category not found');
      }

      const updatedCategory = await this.databaseService.dareCategory.update({
        where: { id },
        data: updateDareCategoryDto,
      });

      return {
        message: 'Dare category updated successfully',
        category: updatedCategory,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('A category with this name already exists');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    const category = await this.databaseService.dareCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: { dares: true },
        },
      },
    });

    if (!category) {
      throw new NotFoundException('Dare category not found');
    }

    // Check if category has associated dares
    if (category._count.dares > 0) {
      throw new ConflictException(
        `Cannot delete category. It has ${category._count.dares} associated dare(s). Please reassign or delete the dares first.`
      );
    }

    await this.databaseService.dareCategory.delete({
      where: { id },
    });

    return {
      message: 'Dare category deleted successfully',
    };
  }

  async toggleStatus(id: number) {
    const category = await this.databaseService.dareCategory.findUnique({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException('Dare category not found');
    }

    const updatedCategory = await this.databaseService.dareCategory.update({
      where: { id },
      data: { isActive: !category.isActive },
    });

    return {
      message: `Dare category ${updatedCategory.isActive ? 'activated' : 'deactivated'} successfully`,
      category: updatedCategory,
    };
  }
}
