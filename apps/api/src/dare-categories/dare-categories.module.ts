import { Module } from '@nestjs/common';
import { DareCategoriesService } from './dare-categories.service';
import { DareCategoriesController } from './dare-categories.controller';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [DareCategoriesController],
  providers: [DareCategoriesService],
  exports: [DareCategoriesService],
})
export class DareCategoriesModule {}
