import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateAreaForImprovementDto, UpdateAreaForImprovementDto } from './dto';
import { Prisma } from '../../generated/prisma';

@Injectable()
export class AreasForImprovementService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createAreaForImprovementDto: CreateAreaForImprovementDto) {
    try {
      const area = await this.databaseService.areaForImprovement.create({
        data: {
          name: createAreaForImprovementDto.name,
          description: createAreaForImprovementDto.description,
          icon: createAreaForImprovementDto.icon,
          isActive: createAreaForImprovementDto.isActive ?? true,
        },
      });

      return {
        message: 'Area for improvement created successfully',
        area,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('An area for improvement with this name already exists');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, search?: string, isActive?: boolean) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.AreaForImprovementWhereInput = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
      ];
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [areas, total] = await Promise.all([
      this.databaseService.areaForImprovement.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.databaseService.areaForImprovement.count({ where }),
    ]);

    return {
      areas,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const area = await this.databaseService.areaForImprovement.findUnique({
      where: { id },
    });

    if (!area) {
      throw new NotFoundException('Area for improvement not found');
    }

    return area;
  }

  async update(id: number, updateAreaForImprovementDto: UpdateAreaForImprovementDto) {
    try {
      const area = await this.databaseService.areaForImprovement.findUnique({
        where: { id },
      });

      if (!area) {
        throw new NotFoundException('Area for improvement not found');
      }

      const updatedArea = await this.databaseService.areaForImprovement.update({
        where: { id },
        data: updateAreaForImprovementDto,
      });

      return {
        message: 'Area for improvement updated successfully',
        area: updatedArea,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('An area for improvement with this name already exists');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    const area = await this.databaseService.areaForImprovement.findUnique({
      where: { id },
    });

    if (!area) {
      throw new NotFoundException('Area for improvement not found');
    }

    await this.databaseService.areaForImprovement.delete({
      where: { id },
    });

    return {
      message: 'Area for improvement deleted successfully',
    };
  }

  async toggleStatus(id: number) {
    const area = await this.databaseService.areaForImprovement.findUnique({
      where: { id },
    });

    if (!area) {
      throw new NotFoundException('Area for improvement not found');
    }

    const updatedArea = await this.databaseService.areaForImprovement.update({
      where: { id },
      data: { isActive: !area.isActive },
    });

    return {
      message: `Area for improvement ${updatedArea.isActive ? 'activated' : 'deactivated'} successfully`,
      area: updatedArea,
    };
  }
}
