import { Module } from '@nestjs/common';
import { AreasForImprovementService } from './areas-for-improvement.service';
import { AreasForImprovementController } from './areas-for-improvement.controller';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [AreasForImprovementController],
  providers: [AreasForImprovementService],
  exports: [AreasForImprovementService],
})
export class AreasForImprovementModule {}
