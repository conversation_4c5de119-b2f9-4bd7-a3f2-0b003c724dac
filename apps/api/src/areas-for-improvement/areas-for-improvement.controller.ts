import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ValidationPipe,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AreasForImprovementService } from './areas-for-improvement.service';
import { CreateAreaForImprovementDto, UpdateAreaForImprovementDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators';
import { Role } from '../../generated/prisma';

@Controller('areas-for-improvement')
@UseGuards(JwtAuthGuard)
export class AreasForImprovementController {
  constructor(private readonly areasForImprovementService: AreasForImprovementService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async create(@Body(ValidationPipe) createAreaForImprovementDto: CreateAreaForImprovementDto) {
    return this.areasForImprovementService.create(createAreaForImprovementDto);
  }

  @Get()
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
    @Query('isActive') isActive?: string,
  ) {
    const isActiveBoolean = isActive === 'true' ? true : isActive === 'false' ? false : undefined;
    return this.areasForImprovementService.findAll(page, limit, search, isActiveBoolean);
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.areasForImprovementService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateAreaForImprovementDto: UpdateAreaForImprovementDto,
  ) {
    return this.areasForImprovementService.update(id, updateAreaForImprovementDto);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.areasForImprovementService.remove(id);
  }

  @Patch(':id/toggle-status')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async toggleStatus(@Param('id', ParseIntPipe) id: number) {
    return this.areasForImprovementService.toggleStatus(id);
  }
}
