import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateInterestDto, UpdateInterestDto } from './dto';
import { Prisma } from '../../generated/prisma';

@Injectable()
export class InterestsService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createInterestDto: CreateInterestDto) {
    try {
      const interest = await this.databaseService.interest.create({
        data: {
          name: createInterestDto.name,
          description: createInterestDto.description,
          icon: createInterestDto.icon,
          isActive: createInterestDto.isActive ?? true,
        },
      });

      return {
        message: 'Interest created successfully',
        interest,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('An interest with this name already exists');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, search?: string, isActive?: boolean) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.InterestWhereInput = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
      ];
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [interests, total] = await Promise.all([
      this.databaseService.interest.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.databaseService.interest.count({ where }),
    ]);

    return {
      interests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const interest = await this.databaseService.interest.findUnique({
      where: { id },
    });

    if (!interest) {
      throw new NotFoundException('Interest not found');
    }

    return interest;
  }

  async update(id: number, updateInterestDto: UpdateInterestDto) {
    try {
      const interest = await this.databaseService.interest.findUnique({
        where: { id },
      });

      if (!interest) {
        throw new NotFoundException('Interest not found');
      }

      const updatedInterest = await this.databaseService.interest.update({
        where: { id },
        data: updateInterestDto,
      });

      return {
        message: 'Interest updated successfully',
        interest: updatedInterest,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('An interest with this name already exists');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    const interest = await this.databaseService.interest.findUnique({
      where: { id },
    });

    if (!interest) {
      throw new NotFoundException('Interest not found');
    }

    await this.databaseService.interest.delete({
      where: { id },
    });

    return {
      message: 'Interest deleted successfully',
    };
  }

  async toggleStatus(id: number) {
    const interest = await this.databaseService.interest.findUnique({
      where: { id },
    });

    if (!interest) {
      throw new NotFoundException('Interest not found');
    }

    const updatedInterest = await this.databaseService.interest.update({
      where: { id },
      data: { isActive: !interest.isActive },
    });

    return {
      message: `Interest ${updatedInterest.isActive ? 'activated' : 'deactivated'} successfully`,
      interest: updatedInterest,
    };
  }
}
