import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { DaresService } from './dares.service';
import { CreateDareDto, UpdateDareDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role, DifficultyLevel } from '../../generated/prisma';

@Controller('dares')
@UseGuards(JwtAuthGuard)
export class DaresController {
  constructor(private readonly daresService: DaresService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  create(@Body(ValidationPipe) createDareDto: CreateDareDto) {
    return this.daresService.create(createDareDto);
  }

  @Get()
  findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('search') search?: string,
    @Query('isActive') isActive?: string,
    @Query('isFeatured') isFeatured?: string,
    @Query('difficulty') difficulty?: DifficultyLevel,
    @Query('categoryId', new ParseIntPipe({ optional: true })) categoryId?: number,
  ) {
    const isActiveBoolean = isActive === 'true' ? true : isActive === 'false' ? false : undefined;
    const isFeaturedBoolean = isFeatured === 'true' ? true : isFeatured === 'false' ? false : undefined;
    
    return this.daresService.findAll(
      page,
      limit,
      search,
      isActiveBoolean,
      isFeaturedBoolean,
      difficulty,
      categoryId,
    );
  }

  @Get('categories')
  getCategories() {
    return this.daresService.getCategories();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.daresService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateDareDto: UpdateDareDto,
  ) {
    return this.daresService.update(id, updateDareDto);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.daresService.remove(id);
  }

  @Patch(':id/toggle-status')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  toggleStatus(@Param('id', ParseIntPipe) id: number) {
    return this.daresService.toggleStatus(id);
  }

  @Patch(':id/toggle-featured')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  toggleFeatured(@Param('id', ParseIntPipe) id: number) {
    return this.daresService.toggleFeatured(id);
  }
}
