import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { CreateDareDto, UpdateDareDto } from './dto';
import { Prisma, DifficultyLevel } from '../../generated/prisma';

@Injectable()
export class DaresService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(createDareDto: CreateDareDto) {
    try {
      // Validate that category exists
      const category = await this.databaseService.dareCategory.findUnique({
        where: { id: createDareDto.categoryId },
      });

      if (!category) {
        throw new BadRequestException('Category not found');
      }

      // Validate creator or company exists if provided
      if (createDareDto.creatorId) {
        const creator = await this.databaseService.user.findUnique({
          where: { id: createDareDto.creatorId },
        });
        if (!creator) {
          throw new BadRequestException('Creator not found');
        }
      }

      if (createDareDto.companyId) {
        const company = await this.databaseService.company.findUnique({
          where: { id: createDareDto.companyId },
        });
        if (!company) {
          throw new BadRequestException('Company not found');
        }
      }

      const dare = await this.databaseService.dare.create({
        data: {
          title: createDareDto.title,
          description: createDareDto.description,
          instructions: createDareDto.instructions,
          difficulty: createDareDto.difficulty,
          pointsToEarn: createDareDto.pointsToEarn,
          pointsToLose: createDareDto.pointsToLose,
          timeLimit: createDareDto.timeLimit,
          categoryId: createDareDto.categoryId,
          creatorId: createDareDto.creatorId,
          companyId: createDareDto.companyId,
          isActive: createDareDto.isActive ?? true,
          isFeatured: createDareDto.isFeatured ?? false,
        },
        include: {
          category: true,
          creator: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return {
        message: 'Dare created successfully',
        dare,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('A dare with this title already exists');
        }
      }
      throw error;
    }
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    isActive?: boolean,
    isFeatured?: boolean,
    difficulty?: DifficultyLevel,
    categoryId?: number,
  ) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.DareWhereInput = {};
    
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } },
        { instructions: { contains: search } },
      ];
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (isFeatured !== undefined) {
      where.isFeatured = isFeatured;
    }

    if (difficulty) {
      where.difficulty = difficulty;
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    const [dares, total] = await Promise.all([
      this.databaseService.dare.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          category: true,
          creator: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              attempts: true,
            },
          },
        },
      }),
      this.databaseService.dare.count({ where }),
    ]);

    return {
      dares,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const dare = await this.databaseService.dare.findUnique({
      where: { id },
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            attempts: true,
          },
        },
      },
    });

    if (!dare) {
      throw new NotFoundException('Dare not found');
    }

    return dare;
  }

  async update(id: number, updateDareDto: UpdateDareDto) {
    try {
      const dare = await this.databaseService.dare.findUnique({
        where: { id },
      });

      if (!dare) {
        throw new NotFoundException('Dare not found');
      }

      // Validate category if provided
      if (updateDareDto.categoryId) {
        const category = await this.databaseService.dareCategory.findUnique({
          where: { id: updateDareDto.categoryId },
        });
        if (!category) {
          throw new BadRequestException('Category not found');
        }
      }

      // Validate creator if provided
      if (updateDareDto.creatorId) {
        const creator = await this.databaseService.user.findUnique({
          where: { id: updateDareDto.creatorId },
        });
        if (!creator) {
          throw new BadRequestException('Creator not found');
        }
      }

      // Validate company if provided
      if (updateDareDto.companyId) {
        const company = await this.databaseService.company.findUnique({
          where: { id: updateDareDto.companyId },
        });
        if (!company) {
          throw new BadRequestException('Company not found');
        }
      }

      const updatedDare = await this.databaseService.dare.update({
        where: { id },
        data: updateDareDto,
        include: {
          category: true,
          creator: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return {
        message: 'Dare updated successfully',
        dare: updatedDare,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('A dare with this title already exists');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    const dare = await this.databaseService.dare.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            attempts: true,
          },
        },
      },
    });

    if (!dare) {
      throw new NotFoundException('Dare not found');
    }

    // Check if dare has attempts - might want to soft delete instead
    if (dare._count.attempts > 0) {
      throw new BadRequestException('Cannot delete dare with existing attempts. Consider deactivating instead.');
    }

    await this.databaseService.dare.delete({
      where: { id },
    });

    return {
      message: 'Dare deleted successfully',
    };
  }

  async toggleStatus(id: number) {
    const dare = await this.databaseService.dare.findUnique({
      where: { id },
    });

    if (!dare) {
      throw new NotFoundException('Dare not found');
    }

    const updatedDare = await this.databaseService.dare.update({
      where: { id },
      data: { isActive: !dare.isActive },
      include: {
        category: true,
      },
    });

    return {
      message: `Dare ${updatedDare.isActive ? 'activated' : 'deactivated'} successfully`,
      dare: updatedDare,
    };
  }

  async toggleFeatured(id: number) {
    const dare = await this.databaseService.dare.findUnique({
      where: { id },
    });

    if (!dare) {
      throw new NotFoundException('Dare not found');
    }

    const updatedDare = await this.databaseService.dare.update({
      where: { id },
      data: { isFeatured: !dare.isFeatured },
      include: {
        category: true,
      },
    });

    return {
      message: `Dare ${updatedDare.isFeatured ? 'featured' : 'unfeatured'} successfully`,
      dare: updatedDare,
    };
  }

  // Helper method to get categories for dropdown
  async getCategories() {
    return this.databaseService.dareCategory.findMany({
      where: { isActive: true },
      select: {
        id: true,
        name: true,
      },
      orderBy: { name: 'asc' },
    });
  }
}
