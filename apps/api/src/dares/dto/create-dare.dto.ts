import { IsString, IsOptional, IsBoolean, IsInt, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { DifficultyLevel } from '../../../generated/prisma';

export class CreateDareDto {
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  title: string;

  @IsString()
  @MinLength(10)
  @MaxLength(500)
  description: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  instructions?: string;

  @IsEnum(DifficultyLevel)
  difficulty: DifficultyLevel;

  @IsInt()
  @Min(1)
  @Max(10000)
  pointsToEarn: number;

  @IsInt()
  @Min(0)
  @Max(10000)
  pointsToLose: number;

  @IsOptional()
  @IsInt()
  @Min(60) // Minimum 1 minute
  @Max(86400) // Maximum 24 hours
  timeLimit?: number;

  @IsInt()
  @Min(1)
  categoryId: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  creatorId?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  companyId?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;
}
