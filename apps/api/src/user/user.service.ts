import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Injectable()
export class UserService {
  constructor(private readonly databaseService: DatabaseService) {}

  async getProfile(userId: number) {
    const user = await this.databaseService.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        phone: true,
        username: true,
        firstName: true,
        lastName: true,
        avatar: true,
        bio: true,
        ageRange: true,
        gender: true,
        country: true,
        role: true,
        isActive: true,
        isVerified: true,
        lastLoginAt: true,
        interests: true,
        areasForImprovement: true,
        totalPoints: true,
        availablePoints: true,
        level: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateProfile(userId: number, updateProfileDto: UpdateProfileDto) {
    const user = await this.databaseService.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.databaseService.user.update({
      where: { id: userId },
      data: updateProfileDto,
      select: {
        id: true,
        email: true,
        phone: true,
        username: true,
        firstName: true,
        lastName: true,
        avatar: true,
        bio: true,
        ageRange: true,
        gender: true,
        country: true,
        interests: true,
        areasForImprovement: true,
        updatedAt: true,
      }
    });

    return {
      message: 'Profile updated successfully',
      user: updatedUser,
    };
  }
}
