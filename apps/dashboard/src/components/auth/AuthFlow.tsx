import React, { useState } from 'react';
import LoginForm from './LoginForm';
import OtpForm from './OtpForm';
import RegisterForm from './RegisterForm';

type AuthStep = 'login' | 'otp' | 'register';

interface AuthFlowProps {
  onAuthComplete: () => void;
}

const AuthFlow: React.FC<AuthFlowProps> = ({ onAuthComplete }) => {
  const [currentStep, setCurrentStep] = useState<AuthStep>('login');
  const [authData, setAuthData] = useState({
    email: '',
    phone: '',
    isNewUser: false,
  });

  const handleOtpSent = (email: string, phone: string, isNewUser: boolean) => {
    setAuthData({ email, phone, isNewUser });
    setCurrentStep('otp');
  };

  const handleOtpVerified = () => {
    if (authData.isNewUser) {
      setCurrentStep('register');
    } else {
      onAuthComplete();
    }
  };

  const handleRegistrationComplete = () => {
    onAuthComplete();
  };

  const handleBackToLogin = () => {
    setCurrentStep('login');
    setAuthData({ email: '', phone: '', isNewUser: false });
  };

  switch (currentStep) {
    case 'login':
      return <LoginForm onOtpSent={handleOtpSent} />;
    
    case 'otp':
      return (
        <OtpForm
          email={authData.email}
          phone={authData.phone}
          isNewUser={authData.isNewUser}
          onVerified={handleOtpVerified}
          onBack={handleBackToLogin}
        />
      );
    
    case 'register':
      return <RegisterForm onRegistered={handleRegistrationComplete} />;
    
    default:
      return <LoginForm onOtpSent={handleOtpSent} />;
  }
};

export default AuthFlow;
