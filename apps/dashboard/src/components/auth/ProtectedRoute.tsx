import React, { useEffect, useState } from 'react';
import { useAuthStore } from '../../stores/authStore';
import AuthFlow from './AuthFlow';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, token } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Small delay to ensure Zustand has loaded from localStorage
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleAuthComplete = () => {
    // Auth complete, component will re-render with user data
  };

  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Check if user is authenticated and has admin role
  const isAuthenticated = user && token && user.role === 'ADMIN';

  if (!isAuthenticated) {
    return <AuthFlow onAuthComplete={handleAuthComplete} />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
