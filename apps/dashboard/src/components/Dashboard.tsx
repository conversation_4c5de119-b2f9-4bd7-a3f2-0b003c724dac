import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './layout/Layout';

// Import page components (will be created)
import DashboardHome from './pages/DashboardHome';
// import Users from './pages/Users';
import Dares from './pages/Dares';
import AttemptedDares from './pages/AttemptedDares';
import DareCategories from './pages/DareCategories';
import AreaForImprovement from './pages/AreaForImprovement';
import Interest from './pages/Interest';
import PointsValue from './pages/PointsValue';
import Withdrawal from './pages/Withdrawal';
import AdSpot from './pages/AdSpot';
import Ads from './pages/Ads';
import Request from './pages/Request';
import Users from './pages/Users';

const Dashboard: React.FC = () => {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/users" element={<Users />} />
        <Route path="/dares" element={<Dares />} />
        <Route path="/attempted-dares" element={<AttemptedDares />} />
        <Route path="/dare-categories" element={<DareCategories />} />
        <Route path="/area-for-improvement" element={<AreaForImprovement />} />
        <Route path="/interest" element={<Interest />} />
        <Route path="/points-value" element={<PointsValue />} />
        <Route path="/withdrawal" element={<Withdrawal />} />
        <Route path="/ad-spot" element={<AdSpot />} />
        <Route path="/ads" element={<Ads />} />
        <Route path="/request" element={<Request />} />
      </Routes>
    </Layout>
  );
};

export default Dashboard;
