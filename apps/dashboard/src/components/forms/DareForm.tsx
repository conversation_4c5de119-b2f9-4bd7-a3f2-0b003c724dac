import React, { useState, useEffect } from 'react';
import type { Dare, CreateDareData, UpdateDareData, DareCategory } from '../../services/daresApi';

interface DareFormProps {
  dare?: Dare;
  categories: DareCategory[];
  onSubmit: (data: CreateDareData | UpdateDareData) => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
  title: string;
}

const DIFFICULTY_OPTIONS = [
  { value: 'EASY', label: 'Easy', color: 'text-green-600' },
  { value: 'MEDIUM', label: 'Medium', color: 'text-yellow-600' },
  { value: 'HARD', label: 'Hard', color: 'text-orange-600' },
  { value: 'EXTREME', label: 'Extreme', color: 'text-red-600' },
];

const DareForm: React.FC<DareFormProps> = ({
  dare,
  categories,
  onSubmit,
  onCancel,
  isLoading,
  title,
}) => {
  const [formData, setFormData] = useState({
    title: dare?.title || '',
    description: dare?.description || '',
    instructions: dare?.instructions || '',
    difficulty: dare?.difficulty || 'EASY',
    pointsToEarn: dare?.pointsToEarn || 100,
    pointsToLose: dare?.pointsToLose || 50,
    timeLimit: dare?.timeLimit || '',
    categoryId: dare?.categoryId || '',
    isActive: dare?.isActive ?? true,
    isFeatured: dare?.isFeatured ?? false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    } else if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (formData.instructions && formData.instructions.length > 1000) {
      newErrors.instructions = 'Instructions must be less than 1000 characters';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Category is required';
    }

    if (formData.pointsToEarn < 1 || formData.pointsToEarn > 10000) {
      newErrors.pointsToEarn = 'Points to earn must be between 1 and 10000';
    }

    if (formData.pointsToLose < 0 || formData.pointsToLose > 10000) {
      newErrors.pointsToLose = 'Points to lose must be between 0 and 10000';
    }

    if (formData.timeLimit && (Number(formData.timeLimit) < 60 || Number(formData.timeLimit) > 86400)) {
      newErrors.timeLimit = 'Time limit must be between 60 seconds (1 minute) and 86400 seconds (24 hours)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const submitData: CreateDareData | UpdateDareData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        instructions: formData.instructions.trim() || undefined,
        difficulty: formData.difficulty as 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME',
        pointsToEarn: Number(formData.pointsToEarn),
        pointsToLose: Number(formData.pointsToLose),
        timeLimit: formData.timeLimit ? Number(formData.timeLimit) : undefined,
        categoryId: Number(formData.categoryId),
        isActive: formData.isActive,
        isFeatured: formData.isFeatured,
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>
      </div>

      {/* Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
          Title *
        </label>
        <input
          type="text"
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.title ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Enter dare title"
          maxLength={100}
        />
        {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description *
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={3}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.description ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Enter dare description"
          maxLength={500}
        />
        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
      </div>

      {/* Instructions */}
      <div>
        <label htmlFor="instructions" className="block text-sm font-medium text-gray-700 mb-1">
          Instructions (Optional)
        </label>
        <textarea
          id="instructions"
          value={formData.instructions}
          onChange={(e) => handleInputChange('instructions', e.target.value)}
          rows={4}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.instructions ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Enter step-by-step instructions"
          maxLength={1000}
        />
        {errors.instructions && <p className="mt-1 text-sm text-red-600">{errors.instructions}</p>}
      </div>

      {/* Category and Difficulty Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-1">
            Category *
          </label>
          <select
            id="categoryId"
            value={formData.categoryId}
            onChange={(e) => handleInputChange('categoryId', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.categoryId ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value="">Select a category</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          {errors.categoryId && <p className="mt-1 text-sm text-red-600">{errors.categoryId}</p>}
        </div>

        <div>
          <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700 mb-1">
            Difficulty *
          </label>
          <select
            id="difficulty"
            value={formData.difficulty}
            onChange={(e) => handleInputChange('difficulty', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {DIFFICULTY_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Points Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="pointsToEarn" className="block text-sm font-medium text-gray-700 mb-1">
            Points to Earn *
          </label>
          <input
            type="number"
            id="pointsToEarn"
            value={formData.pointsToEarn}
            onChange={(e) => handleInputChange('pointsToEarn', e.target.value)}
            min="1"
            max="10000"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.pointsToEarn ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="100"
          />
          {errors.pointsToEarn && <p className="mt-1 text-sm text-red-600">{errors.pointsToEarn}</p>}
        </div>

        <div>
          <label htmlFor="pointsToLose" className="block text-sm font-medium text-gray-700 mb-1">
            Points to Lose *
          </label>
          <input
            type="number"
            id="pointsToLose"
            value={formData.pointsToLose}
            onChange={(e) => handleInputChange('pointsToLose', e.target.value)}
            min="0"
            max="10000"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.pointsToLose ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="50"
          />
          {errors.pointsToLose && <p className="mt-1 text-sm text-red-600">{errors.pointsToLose}</p>}
        </div>
      </div>

      {/* Time Limit */}
      <div>
        <label htmlFor="timeLimit" className="block text-sm font-medium text-gray-700 mb-1">
          Time Limit (seconds, optional)
        </label>
        <input
          type="number"
          id="timeLimit"
          value={formData.timeLimit}
          onChange={(e) => handleInputChange('timeLimit', e.target.value)}
          min="60"
          max="86400"
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.timeLimit ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="3600 (1 hour)"
        />
        <p className="mt-1 text-sm text-gray-500">
          Leave empty for no time limit. Min: 60 seconds (1 minute), Max: 86400 seconds (24 hours)
        </p>
        {errors.timeLimit && <p className="mt-1 text-sm text-red-600">{errors.timeLimit}</p>}
      </div>

      {/* Status Toggles */}
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            checked={formData.isActive}
            onChange={(e) => handleInputChange('isActive', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            Active (users can attempt this dare)
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isFeatured"
            checked={formData.isFeatured}
            onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-700">
            Featured (highlight this dare)
          </label>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : dare ? 'Update Dare' : 'Create Dare'}
        </button>
      </div>
    </form>
  );
};

export default DareForm;
