import React, { useState, useEffect } from 'react';

interface GenericItem {
  id?: number;
  name: string;
  description?: string;
  icon?: string;
  isActive: boolean;
}

interface GenericItemFormProps {
  item?: GenericItem;
  onSubmit: (data: Omit<GenericItem, 'id'>) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  title: string;
  itemType: string; // e.g., "Interest", "Area for Improvement"
}

const GenericItemForm: React.FC<GenericItemFormProps> = ({
  item,
  onSubmit,
  onCancel,
  isLoading = false,
  title,
  itemType,
}) => {
  const [formData, setFormData] = useState<Omit<GenericItem, 'id'>>({
    name: '',
    description: '',
    icon: '',
    isActive: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name,
        description: item.description || '',
        icon: item.icon || '',
        isActive: item.isActive,
      });
    }
  }, [item]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    } else if (formData.name.length > 50) {
      newErrors.name = 'Name must be less than 50 characters';
    }

    if (formData.description && formData.description.length > 255) {
      newErrors.description = 'Description must be less than 255 characters';
    }

    if (formData.icon && formData.icon.length > 100) {
      newErrors.icon = 'Icon must be less than 100 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleChange = (field: keyof typeof formData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Name */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Name *
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => handleChange('name', e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.name ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder={`Enter ${itemType.toLowerCase()} name`}
          disabled={isLoading}
        />
        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleChange('description', e.target.value)}
          rows={3}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.description ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder={`Enter ${itemType.toLowerCase()} description`}
          disabled={isLoading}
        />
        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
      </div>

      {/* Icon */}
      <div>
        <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-2">
          Icon
        </label>
        <input
          type="text"
          id="icon"
          value={formData.icon}
          onChange={(e) => handleChange('icon', e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.icon ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter icon name or emoji"
          disabled={isLoading}
        />
        {errors.icon && <p className="mt-1 text-sm text-red-600">{errors.icon}</p>}
        <p className="mt-1 text-xs text-gray-500">
          You can use emoji (🎯, 💪, 🧠) or icon names
        </p>
      </div>

      {/* Active Status */}
      <div className="flex items-center">
        <input
          type="checkbox"
          id="isActive"
          checked={formData.isActive}
          onChange={(e) => handleChange('isActive', e.target.checked)}
          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          disabled={isLoading}
        />
        <label htmlFor="isActive" className="ml-2 text-sm font-medium text-gray-700">
          Active
        </label>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : item ? `Update ${itemType}` : `Create ${itemType}`}
        </button>
      </div>
    </form>
  );
};

export default GenericItemForm;
