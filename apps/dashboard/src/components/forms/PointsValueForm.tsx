import React, { useState, useEffect } from 'react';
import { PointsValue, CreatePointsValueData, UpdatePointsValueData } from '../../services/pointsValueApi';

interface PointsValueFormProps {
  pointsValue?: PointsValue;
  onSubmit: (data: CreatePointsValueData | UpdatePointsValueData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const PointsValueForm: React.FC<PointsValueFormProps> = ({
  pointsValue,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    country: '',
    pointsCost: '',
    quantity: '',
    icon: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (pointsValue) {
      setFormData({
        name: pointsValue.name || '',
        description: pointsValue.description || '',
        country: pointsValue.country || '',
        pointsCost: pointsValue.pointsCost?.toString() || '',
        quantity: pointsValue.quantity?.toString() || '',
        icon: pointsValue.icon || '',
      });
    }
  }, [pointsValue]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length > 100) {
      newErrors.name = 'Name must be less than 100 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (formData.country && formData.country.length > 50) {
      newErrors.country = 'Country must be less than 50 characters';
    }

    if (!formData.pointsCost.trim()) {
      newErrors.pointsCost = 'Points cost is required';
    } else {
      const pointsCost = parseInt(formData.pointsCost);
      if (isNaN(pointsCost) || pointsCost < 1) {
        newErrors.pointsCost = 'Points cost must be a positive number';
      }
    }

    if (!formData.quantity.trim()) {
      newErrors.quantity = 'Quantity is required';
    } else {
      const quantity = parseInt(formData.quantity);
      if (isNaN(quantity) || quantity < 1) {
        newErrors.quantity = 'Quantity must be a positive number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const submitData: CreatePointsValueData | UpdatePointsValueData = {
      name: formData.name.trim(),
      description: formData.description.trim() || undefined,
      country: formData.country.trim() || undefined,
      pointsCost: parseInt(formData.pointsCost),
      quantity: parseInt(formData.quantity),
      icon: formData.icon.trim() || undefined,
    };

    try {
      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter reward name"
            maxLength={100}
          />
          {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
        </div>

        <div>
          <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
            Country
          </label>
          <input
            type="text"
            id="country"
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.country ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter country (optional)"
            maxLength={50}
          />
          {errors.country && <p className="mt-1 text-sm text-red-600">{errors.country}</p>}
        </div>
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={3}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.description ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter reward description"
          maxLength={500}
        />
        {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="pointsCost" className="block text-sm font-medium text-gray-700 mb-2">
            Points Cost *
          </label>
          <input
            type="number"
            id="pointsCost"
            value={formData.pointsCost}
            onChange={(e) => handleInputChange('pointsCost', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.pointsCost ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter points cost"
            min="1"
          />
          {errors.pointsCost && <p className="mt-1 text-sm text-red-600">{errors.pointsCost}</p>}
        </div>

        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">
            Quantity *
          </label>
          <input
            type="number"
            id="quantity"
            value={formData.quantity}
            onChange={(e) => handleInputChange('quantity', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.quantity ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter quantity"
            min="1"
          />
          {errors.quantity && <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>}
        </div>
      </div>

      <div>
        <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-2">
          Icon URL
        </label>
        <input
          type="url"
          id="icon"
          value={formData.icon}
          onChange={(e) => handleInputChange('icon', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.icon ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter icon URL (optional)"
        />
        {errors.icon && <p className="mt-1 text-sm text-red-600">{errors.icon}</p>}
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {pointsValue ? 'Updating...' : 'Creating...'}
            </div>
          ) : (
            pointsValue ? 'Update Points Value' : 'Create Points Value'
          )}
        </button>
      </div>
    </form>
  );
};

export default PointsValueForm;
