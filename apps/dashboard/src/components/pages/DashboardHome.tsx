import React from 'react';
import StatsCard from '../dashboard/StatsCard';
import ChartCard from '../dashboard/ChartCard';
import SimpleChart from '../dashboard/SimpleChart';
import RecentActivity from '../dashboard/RecentActivity';

const DashboardHome: React.FC = () => {
  // Sample data - in real app, this would come from API
  const statsData = [
    {
      title: 'Total Users',
      value: '3,782',
      change: '+11.01%',
      changeType: 'increase' as const,
      color: 'blue' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
        </svg>
      ),
    },
    {
      title: 'Active Dares',
      value: '1,234',
      change: '+9.05%',
      changeType: 'increase' as const,
      color: 'green' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ),
    },
    {
      title: 'Completed Attempts',
      value: '5,359',
      change: '+15.3%',
      changeType: 'increase' as const,
      color: 'yellow' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      title: 'Total Withdrawals',
      value: '892',
      change: '+7.2%',
      changeType: 'increase' as const,
      color: 'red' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      ),
    },
  ];

  const chartData = [120, 132, 101, 134, 90, 230, 210, 320, 290, 250, 400, 350];
  const chartLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening with your platform.</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
            Export
          </button>
          <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
            Add New
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            icon={stat.icon}
            color={stat.color}
          />
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard
          title="Monthly Activity"
          subtitle="User engagement over time"
          actions={
            <div className="flex items-center space-x-2">
              <button className="text-sm text-gray-500 hover:text-gray-700">View More</button>
              <button className="text-sm text-gray-500 hover:text-gray-700">Delete</button>
            </div>
          }
        >
          <SimpleChart data={chartData} labels={chartLabels} />
        </ChartCard>

        <ChartCard
          title="Monthly Target"
          subtitle="Target you've set for each month"
          actions={
            <div className="flex items-center space-x-2">
              <button className="text-sm text-gray-500 hover:text-gray-700">View More</button>
              <button className="text-sm text-gray-500 hover:text-gray-700">Delete</button>
            </div>
          }
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Target</span>
              <span className="text-lg font-semibold text-gray-900">$20K</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Revenue</span>
              <span className="text-lg font-semibold text-green-600">$18.5K</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: '92.5%' }}></div>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Progress</span>
              <span className="text-green-600 font-medium">+10%</span>
            </div>
          </div>
        </ChartCard>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <RecentActivity />
        </div>
        
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Pending Verifications</span>
                <span className="px-2 py-1 text-xs font-medium text-orange-800 bg-orange-100 rounded-full">23</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Ads</span>
                <span className="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">12</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Support Requests</span>
                <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">5</span>
              </div>
            </div>
          </div>

          {/* Top Categories */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Categories</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Fitness</span>
                <span className="text-sm font-medium text-gray-900">342 dares</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Creative</span>
                <span className="text-sm font-medium text-gray-900">289 dares</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Social</span>
                <span className="text-sm font-medium text-gray-900">156 dares</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardHome;
