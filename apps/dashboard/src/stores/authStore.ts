import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: number;
  email?: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: 'USER' | 'ADMIN' | 'COMPANY_OWNER';
  isActive: boolean;
  isVerified: boolean;
  totalPoints: number;
  availablePoints: number;
  level: number;
}

interface AuthState {
  user: User | null;
  token: string | null;
  loading: boolean;
  login: (
    email: string,
    phone: string,
    country: string
  ) => Promise<{ isNewUser: boolean }>;
  verifyOtp: (email: string, phone: string, otp: string) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      loading: false,

      login: async (email: string, phone: string, country: string) => {
        set({ loading: true });
        try {
          const response = await fetch("/api/auth/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: email || undefined,
              phone: phone || undefined,
              country,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || "Failed to send OTP");
          }

          const data = await response.json();
          set({ loading: false });
          return { isNewUser: data.isNewUser || false };
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },

      verifyOtp: async (email: string, phone: string, otp: string) => {
        set({ loading: true });
        try {
          const response = await fetch("/api/auth/verify-otp", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: email || undefined,
              phone: phone || undefined,
              otp,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || "OTP verification failed");
          }

          const data = await response.json();

          // Check if user has admin role for dashboard access
          if (data.user && data.user.role !== "ADMIN") {
            throw new Error(
              "Access denied. Admin role required for dashboard access."
            );
          }

          set({
            user: data.user,
            token: data.accessToken,
            loading: false,
          });

          // Store token in localStorage for API requests
          if (data.accessToken) {
            localStorage.setItem("authToken", data.accessToken);
          }
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },

      logout: () => {
        localStorage.removeItem("authToken");
        set({ user: null, token: null });
      },

      setUser: (user: User) => {
        set({ user });
      },

      setToken: (token: string) => {
        set({ token });
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
      }),
    }
  )
);
