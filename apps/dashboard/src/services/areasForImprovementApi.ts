interface AreaForImprovement {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateAreaForImprovementData {
  name: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
}

interface UpdateAreaForImprovementData extends Partial<CreateAreaForImprovementData> {}

interface AreasForImprovementResponse {
  areas: AreaForImprovement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface ApiResponse<T> {
  message?: string;
  area?: T;
  areas?: T[];
  pagination?: any;
}

class AreasForImprovementApi {
  private baseUrl = '/api/areas-for-improvement';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('auth-storage');
    let authToken = '';
    
    if (token) {
      try {
        const parsed = JSON.parse(token);
        authToken = parsed.state?.token || '';
      } catch (error) {
        console.error('Error parsing auth token:', error);
      }
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getAreas(params?: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<AreasForImprovementResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    const queryString = searchParams.toString();
    const endpoint = queryString ? `?${queryString}` : '';
    
    return this.request<AreasForImprovementResponse>(endpoint);
  }

  async getArea(id: number): Promise<AreaForImprovement> {
    return this.request<AreaForImprovement>(`/${id}`);
  }

  async createArea(data: CreateAreaForImprovementData): Promise<ApiResponse<AreaForImprovement>> {
    return this.request<ApiResponse<AreaForImprovement>>('', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateArea(id: number, data: UpdateAreaForImprovementData): Promise<ApiResponse<AreaForImprovement>> {
    return this.request<ApiResponse<AreaForImprovement>>(`/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteArea(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  async toggleAreaStatus(id: number): Promise<ApiResponse<AreaForImprovement>> {
    return this.request<ApiResponse<AreaForImprovement>>(`/${id}/toggle-status`, {
      method: 'PATCH',
    });
  }
}

export const areasForImprovementApi = new AreasForImprovementApi();
export type { AreaForImprovement, CreateAreaForImprovementData, UpdateAreaForImprovementData, AreasForImprovementResponse };
