// Types
export interface PointsValue {
  id: number;
  name: string;
  description?: string;
  country?: string;
  pointsCost: number;
  quantity: number;
  isActive: boolean;
  icon?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePointsValueData {
  name: string;
  description?: string;
  country?: string;
  pointsCost: number;
  quantity: number;
  icon?: string;
}

export interface UpdatePointsValueData {
  name?: string;
  description?: string;
  country?: string;
  pointsCost?: number;
  quantity?: number;
  icon?: string;
}

export interface GetPointsValuesParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  country?: string;
}

export interface GetPointsValuesResponse {
  pointsValues: PointsValue[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface ApiResponse<T> {
  message?: string;
  pointsValue?: T;
  pointsValues?: T[];
  pagination?: any;
}

class PointsValueApi {
  private baseUrl = '/api/points-value';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    let authToken = null;
    try {
      const token = localStorage.getItem('authToken');
      if (token) {
        authToken = token;
      }
    } catch (error) {
      console.error('Error parsing auth token:', error);
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getPointsValues(params: GetPointsValuesParams = {}): Promise<GetPointsValuesResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const endpoint = queryString ? `?${queryString}` : '';
    
    return this.request<GetPointsValuesResponse>(endpoint);
  }

  async getPointsValue(id: number): Promise<PointsValue> {
    return this.request<PointsValue>(`/${id}`);
  }

  async createPointsValue(data: CreatePointsValueData): Promise<ApiResponse<PointsValue>> {
    return this.request<ApiResponse<PointsValue>>('', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updatePointsValue(id: number, data: UpdatePointsValueData): Promise<ApiResponse<PointsValue>> {
    return this.request<ApiResponse<PointsValue>>(`/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deletePointsValue(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  async togglePointsValueStatus(id: number): Promise<ApiResponse<PointsValue>> {
    return this.request<ApiResponse<PointsValue>>(`/${id}/toggle-status`, {
      method: 'PATCH',
    });
  }
}

export const pointsValueApi = new PointsValueApi();
