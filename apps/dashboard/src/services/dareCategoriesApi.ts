interface DareCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    dares: number;
  };
}

interface CreateDareCategoryData {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
}

interface UpdateDareCategoryData extends Partial<CreateDareCategoryData> {}

interface DareCategoriesResponse {
  categories: DareCategory[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface ApiResponse<T> {
  message?: string;
  category?: T;
  categories?: T[];
  pagination?: any;
}

class DareCategoriesApi {
  private baseUrl = '/api/dare-categories';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('auth-storage');
    let authToken = '';
    
    if (token) {
      try {
        const parsed = JSON.parse(token);
        authToken = parsed.state?.token || '';
      } catch (error) {
        console.error('Error parsing auth token:', error);
      }
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getCategories(params?: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<DareCategoriesResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    const queryString = searchParams.toString();
    const endpoint = queryString ? `?${queryString}` : '';
    
    return this.request<DareCategoriesResponse>(endpoint);
  }

  async getCategory(id: number): Promise<DareCategory> {
    return this.request<DareCategory>(`/${id}`);
  }

  async createCategory(data: CreateDareCategoryData): Promise<ApiResponse<DareCategory>> {
    return this.request<ApiResponse<DareCategory>>('', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateCategory(id: number, data: UpdateDareCategoryData): Promise<ApiResponse<DareCategory>> {
    return this.request<ApiResponse<DareCategory>>(`/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteCategory(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  async toggleCategoryStatus(id: number): Promise<ApiResponse<DareCategory>> {
    return this.request<ApiResponse<DareCategory>>(`/${id}/toggle-status`, {
      method: 'PATCH',
    });
  }
}

export const dareCategoriesApi = new DareCategoriesApi();
export type { DareCategory, CreateDareCategoryData, UpdateDareCategoryData, DareCategoriesResponse };
