interface Interest {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CreateInterestData {
  name: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
}

interface UpdateInterestData extends Partial<CreateInterestData> {}

interface InterestsResponse {
  interests: Interest[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface ApiResponse<T> {
  message?: string;
  interest?: T;
  interests?: T[];
  pagination?: any;
}

class InterestsApi {
  private baseUrl = '/api/interests';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('auth-storage');
    let authToken = '';
    
    if (token) {
      try {
        const parsed = JSON.parse(token);
        authToken = parsed.state?.token || '';
      } catch (error) {
        console.error('Error parsing auth token:', error);
      }
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getInterests(params?: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<InterestsResponse> {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    const queryString = searchParams.toString();
    const endpoint = queryString ? `?${queryString}` : '';
    
    return this.request<InterestsResponse>(endpoint);
  }

  async getInterest(id: number): Promise<Interest> {
    return this.request<Interest>(`/${id}`);
  }

  async createInterest(data: CreateInterestData): Promise<ApiResponse<Interest>> {
    return this.request<ApiResponse<Interest>>('', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateInterest(id: number, data: UpdateInterestData): Promise<ApiResponse<Interest>> {
    return this.request<ApiResponse<Interest>>(`/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteInterest(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  async toggleInterestStatus(id: number): Promise<ApiResponse<Interest>> {
    return this.request<ApiResponse<Interest>>(`/${id}/toggle-status`, {
      method: 'PATCH',
    });
  }
}

export const interestsApi = new InterestsApi();
export type { Interest, CreateInterestData, UpdateInterestData, InterestsResponse };
