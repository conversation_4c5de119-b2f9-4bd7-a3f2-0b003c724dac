import { apiClient } from './apiClient';

export interface DareCategory {
  id: number;
  name: string;
}

export interface DareCreator {
  id: number;
  username: string;
  firstName?: string;
  lastName?: string;
}

export interface DareCompany {
  id: number;
  name: string;
}

export interface Dare {
  id: number;
  title: string;
  description: string;
  instructions?: string;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME';
  pointsToEarn: number;
  pointsToLose: number;
  timeLimit?: number;
  categoryId: number;
  category: DareCategory;
  creatorId?: number;
  creator?: DareCreator;
  companyId?: number;
  company?: DareCompany;
  isActive: boolean;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    attempts: number;
  };
}

export interface CreateDareData {
  title: string;
  description: string;
  instructions?: string;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME';
  pointsToEarn: number;
  pointsToLose: number;
  timeLimit?: number;
  categoryId: number;
  creatorId?: number;
  companyId?: number;
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface UpdateDareData extends Partial<CreateDareData> {}

export interface GetDaresParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME';
  categoryId?: number;
}

export interface GetDaresResponse {
  dares: Dare[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const daresApi = {
  async getDares(params: GetDaresParams = {}): Promise<GetDaresResponse> {
    try {
      const response = await apiClient.get('/dares', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch dares');
    }
  },

  async getDare(id: number): Promise<Dare> {
    try {
      const response = await apiClient.get(`/dares/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch dare');
    }
  },

  async createDare(data: CreateDareData): Promise<{ message: string; dare: Dare }> {
    try {
      const response = await apiClient.post('/dares', data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create dare');
    }
  },

  async updateDare(id: number, data: UpdateDareData): Promise<{ message: string; dare: Dare }> {
    try {
      const response = await apiClient.patch(`/dares/${id}`, data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update dare');
    }
  },

  async deleteDare(id: number): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`/dares/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete dare');
    }
  },

  async toggleDareStatus(id: number): Promise<{ message: string; dare: Dare }> {
    try {
      const response = await apiClient.patch(`/dares/${id}/toggle-status`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to toggle dare status');
    }
  },

  async toggleDareFeatured(id: number): Promise<{ message: string; dare: Dare }> {
    try {
      const response = await apiClient.patch(`/dares/${id}/toggle-featured`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to toggle dare featured status');
    }
  },

  async getCategories(): Promise<DareCategory[]> {
    try {
      const response = await apiClient.get('/dares/categories');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch categories');
    }
  },
};
