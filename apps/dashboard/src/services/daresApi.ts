// No imports needed - using fetch directly

export interface DareCategory {
  id: number;
  name: string;
}

export interface DareCreator {
  id: number;
  username: string;
  firstName?: string;
  lastName?: string;
}

export interface DareCompany {
  id: number;
  name: string;
}

export interface Dare {
  id: number;
  title: string;
  description: string;
  instructions?: string;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME';
  pointsToEarn: number;
  pointsToLose: number;
  timeLimit?: number;
  categoryId: number;
  category: DareCategory;
  creatorId?: number;
  creator?: DareCreator;
  companyId?: number;
  company?: DareCompany;
  isActive: boolean;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    attempts: number;
  };
}

export interface CreateDareData {
  title: string;
  description: string;
  instructions?: string;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME';
  pointsToEarn: number;
  pointsToLose: number;
  timeLimit?: number;
  categoryId: number;
  creatorId?: number;
  companyId?: number;
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface UpdateDareData extends Partial<CreateDareData> {}

export interface GetDaresParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD' | 'EXTREME';
  categoryId?: number;
}

export interface GetDaresResponse {
  dares: Dare[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface ApiResponse<T> {
  message?: string;
  dare?: T;
  dares?: T[];
  pagination?: any;
}

class DaresApi {
  private baseUrl = '/api/dares';

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    let authToken = null;
    try {
      const token = localStorage.getItem('authToken');
      if (token) {
        authToken = token;
      }
    } catch (error) {
      console.error('Error parsing auth token:', error);
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getDares(params: GetDaresParams = {}): Promise<GetDaresResponse> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const endpoint = queryString ? `?${queryString}` : '';

    return this.request<GetDaresResponse>(endpoint);
  }

  async getDare(id: number): Promise<Dare> {
    return this.request<Dare>(`/${id}`);
  }

  async createDare(data: CreateDareData): Promise<ApiResponse<Dare>> {
    return this.request<ApiResponse<Dare>>('', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateDare(id: number, data: UpdateDareData): Promise<ApiResponse<Dare>> {
    return this.request<ApiResponse<Dare>>(`/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteDare(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  async toggleDareStatus(id: number): Promise<ApiResponse<Dare>> {
    return this.request<ApiResponse<Dare>>(`/${id}/toggle-status`, {
      method: 'PATCH',
    });
  }

  async toggleDareFeatured(id: number): Promise<ApiResponse<Dare>> {
    return this.request<ApiResponse<Dare>>(`/${id}/toggle-featured`, {
      method: 'PATCH',
    });
  }

  async getCategories(): Promise<DareCategory[]> {
    // Categories are fetched from the dare-categories endpoint
    const response = await fetch('/api/dare-categories', {
      headers: {
        'Content-Type': 'application/json',
        ...(localStorage.getItem('authToken') && {
          Authorization: `Bearer ${localStorage.getItem('authToken')}`
        }),
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to fetch categories');
    }

    const data = await response.json();
    return data.categories || data;
  }
}

export const daresApi = new DaresApi();
